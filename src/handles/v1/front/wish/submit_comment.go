package wish

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 发表评论
func SubmitComment(ctx *gin.Context) (any, error) {
	var in struct {
		WishId    uint   `form:"wish_id" binding:"required"`
		CommentId uint   `form:"comment_id"`
		Content   string `form:"content" binding:"required"`
		IsPrivate int    `form:"is_private"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	if unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "wishComment", convertor.ToString(session.UserId)), constmap.TimeDur1m); err != nil {
		return nil, err
	} else {
		defer unlocker()
	}

	var wish = new(models.Wish)
	if db.Take(&wish, in.WishId).Error != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}
	if wish.State != constmap.WishStateProcessing && wish.State != constmap.WishStateSuccess {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "心愿单不可发布评论")
	}

	var parentComment = new(models.WishComment)
	if in.CommentId > 0 {
		if db.Where(models.WishComment{
			WishId: in.WishId,
			State:  constmap.WishCommentStateApproved,
		}).Take(&parentComment, in.CommentId).Error != nil {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, "评论不存在")
		}
		if parentComment.UserId == session.UserId {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, "不能回复自己的评论内容")
		}
	}

	var comment = &models.WishComment{
		WishId:    in.WishId,
		UserId:    session.UserId,
		State:     constmap.WishCommentStateWaitReview,
		Content:   in.Content,
		IsPrivate: utils.If(in.IsPrivate == constmap.Enable, constmap.Enable, constmap.Disable),
	}
	if parentComment.ID > 0 {
		if parentComment.ReplyCommentId > 0 {
			//回复一条回复
			comment.ParentCommentId = parentComment.ParentCommentId
			comment.ReplyCommentId = parentComment.ID
			comment.ReplyUserId = parentComment.UserId
		} else {
			//回复一条评论
			comment.ParentCommentId = parentComment.ID
		}
	}
	if err := db.Create(&comment).Error; err != nil {
		return nil, utils.NewError(err)
	}

	// 发送异步审核消息到weight队列（耗时操作）
	if err := my_queue.Weight(constmap.EventWishComment, gin.H{
		"comment_id": comment.ID,
	}); err != nil {
		my_logger.Errorf("发送心愿单评论审核事件失败", zap.Uint("commentId", comment.ID), zap.Error(err))
		// 队列消息发送失败不影响主流程
	} else {
		my_logger.Infof("发送心愿单评论审核事件成功", zap.Uint("commentId", comment.ID))
	}

	var out struct {
		Id uint `json:"id"`
	}
	out.Id = comment.ID
	return out, nil
}
