package message_biz

import (
	"fmt"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"time"
)

// MessageVO 消息视图对象
type MessageVO struct {
	Id        uint                    `json:"id"`
	Type      constmap.MessageType    `json:"type"`
	SubType   constmap.MessageSubType `json:"sub_type"`
	Title     string                  `json:"title"`
	Content   string                  `json:"content"`
	IsRead    bool                    `json:"is_read"`
	ReadTime  int64                   `json:"read_time"`  // unix时间戳，0表示未读
	CreatedAt int64                   `json:"created_at"` // unix时间戳

	// 业务字段
	WishCommentId uint `json:"wish_comment_id,omitempty"`
	WishMemberId  uint `json:"wish_member_id,omitempty"`
}

// MessageTypeSummary 消息类型汇总
type MessageTypeSummary struct {
	Total       int64        `json:"total"`
	UnreadCount int64        `json:"unread_count"`
	Messages    []*MessageVO `json:"messages"`
}

// MessageBadge 消息徽章
type MessageBadge struct {
	SystemUnread  int64 `json:"system_unread"`
	TeamUnread    int64 `json:"team_unread"`
	CommentUnread int64 `json:"comment_unread"`
	TotalUnread   int64 `json:"total_unread"`
}

// 获取用户消息列表 - 统一版本
func GetUserMessages(db *gorm.DB, userId uint, msgType constmap.MessageType, limit int) ([]*MessageVO, error) {
	var results []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	// 统一查询：所有消息都通过messages表关联
	err := db.Model(&models.MessageTpl{}).
		Select("message_tpls.*, messages.is_read, messages.read_time").
		Joins("INNER JOIN messages ON message_tpls.id = messages.message_tpl_id").
		Where("message_tpls.type = ? AND messages.user_id = ?", msgType, userId).
		Order("message_tpls.created_at DESC").
		Limit(limit).
		Find(&results).Error

	if err != nil {
		return nil, utils.NewError(err)
	}

	// 转换为MessageVO
	return ConvertToMessageVOs(results), nil
}

// 获取消息统计数量（简化版：直接查询，无缓存）
func GetMessageCounts(db *gorm.DB, userId uint, msgType constmap.MessageType) (total, unreadCount int64, err error) {
	// 直接查询用户的消息统计
	err = db.Model(&models.Message{}).
		Select("COUNT(*) as total, COUNT(CASE WHEN is_read = 2 THEN 1 END) as unread").
		Joins("INNER JOIN message_tpls ON message_tpls.id = messages.message_tpl_id").
		Where("message_tpls.type = ? AND messages.user_id = ?", msgType, userId).
		Row().Scan(&total, &unreadCount)
	if err != nil {
		return 0, 0, utils.NewError(err)
	}

	return total, unreadCount, nil
}

// 获取消息徽章（未读数统计）优化版：批量查询
func GetMessageBadge(db *gorm.DB, userId uint) (*MessageBadge, error) {
	// 尝试从缓存获取
	cacheKey := fmt.Sprintf(constmap.RKMsgBadge, userId)
	var badge = &utils.Marshaller[MessageBadge]{new(MessageBadge)}

	if my_cache.Get(cacheKey, &badge) {
		return badge.Data, nil
	}

	// 缓存未命中，统一查询各类型消息的未读数
	var results []struct {
		Type        int   `gorm:"column:type"`
		UnreadCount int64 `gorm:"column:unread_count"`
	}

	err := db.Model(&models.Message{}).
		Select("message_tpls.type, COUNT(CASE WHEN messages.is_read = 2 THEN 1 END) as unread_count").
		Joins("INNER JOIN message_tpls ON message_tpls.id = messages.message_tpl_id").
		Where("messages.user_id = ?", userId).
		Where("message_tpls.type IN ?", []constmap.MessageType{
			constmap.MessageTypeSystem,
			constmap.MessageTypeTeam,
			constmap.MessageTypeComment,
		}).
		Group("message_tpls.type").
		Find(&results).Error

	if err != nil {
		return nil, utils.NewError(err)
	}

	badge.Data = &MessageBadge{}
	badgeData := badge.Data

	for _, result := range results {
		switch constmap.MessageType(result.Type) {
		case constmap.MessageTypeSystem:
			badgeData.SystemUnread = result.UnreadCount
		case constmap.MessageTypeTeam:
			badgeData.TeamUnread = result.UnreadCount
		case constmap.MessageTypeComment:
			badgeData.CommentUnread = result.UnreadCount
		}
	}

	badgeData.TotalUnread = badgeData.SystemUnread + badgeData.TeamUnread + badgeData.CommentUnread

	_ = my_cache.Set(cacheKey, badge, constmap.TimeDur10m)

	return badge.Data, nil
}

// 批量设置消息已读（仅普通文本消息）
func BatchSetMessageRead(db *gorm.DB, userId uint, messageIds []uint) error {
	if len(messageIds) == 0 {
		return nil
	}

	// 查询符合条件的消息模板（仅普通文本消息且属于当前用户）
	var validTemplates []*models.MessageTpl
	err := db.Model(&models.MessageTpl{}).
		Joins("INNER JOIN messages ON message_tpls.id = messages.message_tpl_id").
		Where("message_tpls.id IN ?", messageIds).
		Where("message_tpls.sub_type = ?", constmap.MessageSubTypeText).
		Where("messages.user_id = ?", userId).
		Find(&validTemplates).Error
	if err != nil {
		return utils.NewError(err)
	}

	if len(validTemplates) == 0 {
		return nil
	}

	validIds := make([]uint, len(validTemplates))
	for i, tpl := range validTemplates {
		validIds[i] = tpl.ID
	}

	// 批量创建或更新消息状态
	now := time.Now()
	var messages []*models.Message

	for _, templateId := range validIds {
		messages = append(messages, &models.Message{
			MessageTplId: templateId,
			UserId:       userId,
			IsRead:       constmap.Enable,
			ReadTime:     &now,
		})
	}

	// 使用事务批量插入或更新
	err = db.Transaction(func(tx *gorm.DB) error {
		for _, msg := range messages {
			err := tx.Where(models.Message{MessageTplId: msg.MessageTplId, UserId: msg.UserId}).
				Assign(models.Message{IsRead: constmap.Enable, ReadTime: &now}).
				FirstOrCreate(msg).Error
			if err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		return utils.NewError(err)
	}

	// 清除相关缓存 - 只清除受影响的MessageType
	affectedTypes := extractMessageTypes(validTemplates)
	ClearUserMessageCacheByTypes(userId, affectedTypes)

	return nil
}

// 批量获取指定类型消息汇总（按类型分别缓存）
func GetMessageTypeSummaries(db *gorm.DB, userId uint, messageTypes []constmap.MessageType, messageLimit int) (map[constmap.MessageType]*MessageTypeSummary, error) {
	summaryMap := make(map[constmap.MessageType]*MessageTypeSummary)

	for _, msgType := range messageTypes {
		cacheKey := fmt.Sprintf(constmap.RKMsgTypeSummary, userId, msgType)
		summary := &utils.Marshaller[MessageTypeSummary]{new(MessageTypeSummary)}

		if my_cache.Get(cacheKey, &summary) {
			summaryMap[msgType] = summary.Data
			continue
		}

		// 缓存未命中，查询数据
		userMessages, err := GetUserMessages(db, userId, msgType, messageLimit)
		if err != nil {
			return nil, err
		}

		total, unreadCount, err := GetMessageCounts(db, userId, msgType)
		if err != nil {
			return nil, err
		}

		summary.Data = &MessageTypeSummary{
			Total:       total,
			UnreadCount: unreadCount,
			Messages:    userMessages,
		}

		summaryMap[msgType] = summary.Data
		_ = my_cache.Set(cacheKey, summary, constmap.TimeDur10m)
	}

	return summaryMap, nil
}

// 清除用户消息相关缓存（按MessageType精确清理）
func ClearUserMessageCacheByTypes(userId uint, messageTypes []constmap.MessageType) {
	if len(messageTypes) == 0 {
		return
	}

	// 收集需要删除的缓存key
	var keys []string

	// 消息徽章缓存（包含所有类型的统计，需要清除）
	keys = append(keys, fmt.Sprintf(constmap.RKMsgBadge, userId))

	// 只清除受影响的消息类型缓存
	for _, msgType := range messageTypes {
		// 消息汇总缓存
		keys = append(keys, fmt.Sprintf(constmap.RKMsgTypeSummary, userId, msgType))
	}

	// 使用pipeline批量删除
	my_cache.RedisClient().Del(keys...)
}

// 辅助函数：提取消息类型列表（去重）
func extractMessageTypes(templates []*models.MessageTpl) []constmap.MessageType {
	// 使用typeset去重
	typeSet := typeset.NewTypeSet[constmap.MessageType](false)
	for _, tpl := range templates {
		typeSet.Add(tpl.Type)
	}

	return typeSet.Values()
}

// 转换查询结果为MessageVO格式（公共函数）
func ConvertToMessageVOs(results []struct {
	models.MessageTpl
	IsRead   int        `gorm:"column:is_read"`
	ReadTime *time.Time `gorm:"column:read_time"`
}) []*MessageVO {
	var messages = make([]*MessageVO, 0, len(results))
	for _, result := range results {
		vo := &MessageVO{
			Id:            result.ID,
			Type:          result.Type,
			SubType:       result.SubType,
			Title:         result.Title,
			Content:       result.Content,
			IsRead:        result.IsRead == constmap.Enable,
			CreatedAt:     result.CreatedAt.Unix(),
			WishCommentId: result.WishCommentId,
			WishMemberId:  result.WishMemberId,
		}
		if result.ReadTime != nil {
			vo.ReadTime = result.ReadTime.Unix()
		}
		messages = append(messages, vo)
	}
	return messages
}

// 创建消息模板的通用方法
func CreateMessageTemplate(db *gorm.DB, msgType constmap.MessageType, subType constmap.MessageSubType, title, content string, wishMemberId uint) (*models.MessageTpl, error) {
	messageTpl := &models.MessageTpl{
		Type:         msgType,
		SubType:      subType,
		Title:        title,
		Content:      content,
		WishMemberId: wishMemberId,
	}

	if err := db.Create(messageTpl).Error; err != nil {
		return nil, utils.NewError(err)
	}

	return messageTpl, nil
}

// 发送组队邀请消息
func SendTeamInviteMessage(db *gorm.DB, wishId, fromUserId, toUserId, memberId uint) error {
	// 查询心愿单信息
	var wish models.Wish
	if err := db.Take(&wish, wishId).Error; err != nil {
		return utils.NewError(err)
	}

	// 查询发起人信息
	var fromUser models.User
	if err := db.Take(&fromUser, fromUserId).Error; err != nil {
		return utils.NewError(err)
	}

	// 构建消息内容
	title := "心愿单组队邀请"
	content := fmt.Sprintf("%s 邀请您加入心愿单「%s」，一起去 %s", fromUser.Nickname, wish.Title, wish.To)

	// 使用事务创建消息模板和用户消息记录
	err := db.Transaction(func(tx *gorm.DB) error {
		// 创建消息模板
		messageTpl := &models.MessageTpl{
			Type:         constmap.MessageTypeTeam,
			SubType:      constmap.MessageSubTypeTeamInvite,
			Title:        title,
			Content:      content,
			WishMemberId: memberId,
		}

		if err := tx.Create(messageTpl).Error; err != nil {
			return err
		}

		// 创建用户消息记录（未读状态）
		message := &models.Message{
			MessageTplId: messageTpl.ID,
			UserId:       toUserId,
			IsRead:       constmap.Disable, // 未读
		}

		if err := tx.Create(message).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return utils.NewError(err)
	}

	// 清除相关缓存
	ClearUserMessageCacheByTypes(toUserId, []constmap.MessageType{constmap.MessageTypeTeam})

	return nil
}

// 发送邀请回复消息给发起人
func SendTeamResponseMessage(db *gorm.DB, wishId, fromUserId, toUserId uint, accepted bool, wishTitle, rejectReason string) error {
	// 查询回复人信息
	var fromUser models.User
	if err := db.Take(&fromUser, fromUserId).Error; err != nil {
		return utils.NewError(err)
	}

	// 构建消息内容
	var title, content string
	if accepted {
		title = "组队邀请已同意"
		content = fmt.Sprintf("%s 同意了您的邀请，加入心愿单「%s」", fromUser.Nickname, wishTitle)
	} else {
		title = "组队邀请已拒绝"
		if rejectReason != "" {
			content = fmt.Sprintf("%s 拒绝了您的邀请，未能加入心愿单「%s」。拒绝原因：%s", fromUser.Nickname, wishTitle, rejectReason)
		} else {
			content = fmt.Sprintf("%s 拒绝了您的邀请，未能加入心愿单「%s」", fromUser.Nickname, wishTitle)
		}
	}

	// 使用事务创建消息模板和用户消息记录
	err := db.Transaction(func(tx *gorm.DB) error {
		// 创建消息模板
		messageTpl := &models.MessageTpl{
			Type:         constmap.MessageTypeTeam,
			SubType:      constmap.MessageSubTypeText,
			Title:        title,
			Content:      content,
			WishMemberId: 0, // 回复消息不关联具体的成员记录
		}

		if err := tx.Create(messageTpl).Error; err != nil {
			return err
		}

		// 创建用户消息记录（未读状态）
		message := &models.Message{
			MessageTplId: messageTpl.ID,
			UserId:       toUserId,
			IsRead:       constmap.Disable, // 未读
		}

		if err := tx.Create(message).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return utils.NewError(err)
	}

	// 清除相关缓存
	ClearUserMessageCacheByTypes(toUserId, []constmap.MessageType{constmap.MessageTypeTeam})

	return nil
}

// 发送评论审核通过消息
func SendCommentApprovedMessage(db *gorm.DB, commentId uint) error {
	// 查询评论信息
	var comment models.WishComment
	if err := db.Take(&comment, commentId).Error; err != nil {
		return utils.NewError(err)
	}

	// 查询关联的心愿单信息
	var wish models.Wish
	if err := db.Take(&wish, comment.WishId).Error; err != nil {
		return utils.NewError(err)
	}

	// 查询评论者信息
	commentUserMap := user_biz.LoadUsers(db, []uint{comment.UserId})
	commentUser, exists := commentUserMap[comment.UserId]
	if !exists {
		return utils.NewErrorStr(constmap.ErrorSystem, "评论者信息不存在")
	}

	// 确定消息接收人
	var toUserId uint
	var title, content string

	if comment.ReplyUserId > 0 {
		// 有被回复人，给被回复人发消息
		toUserId = comment.ReplyUserId
		title = "您的评论收到新回复"
		content = fmt.Sprintf("%s 回复了您在心愿单「%s」中的评论：%s", commentUser.Nickname, wish.Title, comment.Content)
	} else {
		// 没有被回复人，给心愿单发起人发消息
		toUserId = wish.UserId
		title = "心愿单收到新评论"
		content = fmt.Sprintf("%s 评论了您的心愿单「%s」：%s", commentUser.Nickname, wish.Title, comment.Content)
	}

	// 如果接收人是评论者本人，不发送消息
	if toUserId == comment.UserId {
		return nil
	}

	// 使用事务创建消息模板和用户消息记录
	err := db.Transaction(func(tx *gorm.DB) error {
		// 创建消息模板
		messageTpl := &models.MessageTpl{
			Type:          constmap.MessageTypeComment,
			SubType:       constmap.MessageSubTypeText,
			Title:         title,
			Content:       content,
			WishCommentId: commentId,
			WishMemberId:  0,
		}

		if err := tx.Create(messageTpl).Error; err != nil {
			return err
		}

		// 创建用户消息记录（未读状态）
		message := &models.Message{
			MessageTplId: messageTpl.ID,
			UserId:       toUserId,
			IsRead:       constmap.Disable, // 未读
		}

		if err := tx.Create(message).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return utils.NewError(err)
	}

	// 清除相关缓存
	ClearUserMessageCacheByTypes(toUserId, []constmap.MessageType{constmap.MessageTypeComment})

	return nil
}

// 查询用户消息详情（消息模板+用户消息状态）
func GetUserMessageDetail(db *gorm.DB, messageId, userId uint) (*beans.UserMessageDetail, error) {
	var detail beans.UserMessageDetail

	// 一次查询获取消息模板和用户消息状态
	err := db.Model(&models.MessageTpl{}).
		Select(`message_tpls.id, message_tpls.type, message_tpls.sub_type, message_tpls.title,
				message_tpls.content, message_tpls.created_at, message_tpls.wish_comment_id,
				message_tpls.wish_member_id, messages.is_read, messages.read_time`).
		Joins("INNER JOIN messages ON message_tpls.id = messages.message_tpl_id").
		Where("message_tpls.id = ?", messageId).
		Where("messages.user_id = ?", userId).
		First(&detail).Error
	if err != nil {
		return nil, utils.NewError(err)
	}

	return &detail, nil
}
