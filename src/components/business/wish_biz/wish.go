package wish_biz

import (
	"fmt"
	"gorm.io/gorm"
	"regexp"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_baidu"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"go.uber.org/zap"
)

func GetHotZonesCc(db *gorm.DB) ([]beans.WishHotZone, error) {
	ckey := fmt.Sprintf(constmap.RKWishTop, "zone")
	v := &utils.Marshaller[[]beans.WishHotZone]{}
	if my_cache.Get(ckey, v) {
		ret := *v.Data
		return utils.If(len(ret) > 0, ret, make([]beans.WishHotZone, 0)), nil
	}

	// 从ES查询热门目的地城市
	query := map[string]any{
		"size": 0,
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"terms": map[string]any{
							"state": []constmap.WishState{constmap.WishStateSuccess, constmap.WishStateProcessing},
						},
					},
					{
						"term": map[string]any{
							"open_scope": constmap.Enable,
						},
					},
				},
			},
		},
		"aggs": map[string]any{
			"hot_zones": map[string]any{
				"terms": map[string]any{
					"field": "to_zone_id",
					"size":  10,
					"order": map[string]any{
						"_count": "desc",
					},
				},
			},
		},
	}

	// 执行ES聚合查询
	var result struct {
		Aggregations struct {
			HotZones struct {
				Buckets []struct {
					Key      uint  `json:"key"`
					DocCount int64 `json:"doc_count"`
				} `json:"buckets"`
			} `json:"hot_zones"`
		} `json:"aggregations"`
	}

	if err := es.SearchWithAggregations(constmap.EsIndexWish, query, &result); err != nil {
		return make([]beans.WishHotZone, 0), err
	}

	// 获取城市名称
	var zoneIds []uint
	for _, bucket := range result.Aggregations.HotZones.Buckets {
		zoneIds = append(zoneIds, bucket.Key)
	}

	// 构建结果
	zoneMap := zone_biz.NewZoneBiz().GetZoneMap(db, zoneIds)

	var hotZones = make([]beans.WishHotZone, 0)
	for _, bucket := range result.Aggregations.HotZones.Buckets {
		zone := zoneMap[bucket.Key]
		zoneName := zone.Name
		if zoneName == "" {
			zoneName = "未知城市"
		}
		hotZones = append(hotZones, beans.WishHotZone{
			Id:   bucket.Key,
			Name: zoneName,
			Poi:  utils.JoinPoi(zone.Lng, zone.Lat),
		})
	}

	// 缓存结果
	v.Data = &hotZones
	if err := my_cache.Set(ckey, v, constmap.TimeDur1d+utils.RandTime(time.Hour)); err != nil {
		// 缓存失败不影响返回结果，只记录错误
		fmt.Printf("缓存热门城市数据失败: %v\n", err)
	}

	return hotZones, nil
}

func GetHotTagsCc() ([]beans.WishHotTag, error) {
	ckey := fmt.Sprintf(constmap.RKWishTop, "tag")
	v := &utils.Marshaller[[]beans.WishHotTag]{}
	if my_cache.Get(ckey, v) {
		ret := *v.Data
		return utils.If(len(ret) > 0, ret, make([]beans.WishHotTag, 0)), nil
	}

	// 从ES查询热门标签
	query := map[string]any{
		"size": 0,
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"terms": map[string]any{
							"state": []constmap.WishState{constmap.WishStateSuccess, constmap.WishStateProcessing},
						},
					},
					{
						"term": map[string]any{
							"open_scope": constmap.Enable,
						},
					},
				},
			},
		},
		"aggs": map[string]any{
			"hot_tags": map[string]any{
				"terms": map[string]any{
					"field": "tags",
					"size":  5,
					"order": map[string]any{
						"_count": "desc",
					},
				},
			},
		},
	}

	// 执行ES聚合查询
	var result struct {
		Aggregations struct {
			HotTags struct {
				Buckets []struct {
					Key      string `json:"key"`
					DocCount int64  `json:"doc_count"`
				} `json:"buckets"`
			} `json:"hot_tags"`
		} `json:"aggregations"`
	}

	if err := es.SearchWithAggregations(constmap.EsIndexWish, query, &result); err != nil {
		return make([]beans.WishHotTag, 0), err
	}

	// 构建结果
	var hotTags = make([]beans.WishHotTag, 0)
	for _, bucket := range result.Aggregations.HotTags.Buckets {
		hotTags = append(hotTags, beans.WishHotTag{
			Tag:   bucket.Key,
			Count: bucket.DocCount,
		})
	}

	// 缓存结果
	v.Data = &hotTags
	if err := my_cache.Set(ckey, v, constmap.TimeDur1d+utils.RandTime(time.Hour)); err != nil {
		// 缓存失败不影响返回结果，只记录错误
		fmt.Printf("缓存热门标签数据失败: %v\n", err)
	}

	return hotTags, nil
}

func GetHotDayTagsCc() ([]beans.WishHotDayTag, error) {
	ckey := fmt.Sprintf(constmap.RKWishTop, "dayTag")
	v := &utils.Marshaller[[]beans.WishHotDayTag]{}
	if my_cache.Get(ckey, v) {
		ret := *v.Data
		return utils.If(len(ret) > 0, ret, make([]beans.WishHotDayTag, 0)), nil
	}

	// 从ES查询热门节假日
	query := map[string]any{
		"size": 0,
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"terms": map[string]any{
							"state": []constmap.WishState{constmap.WishStateSuccess, constmap.WishStateProcessing},
						},
					},
					{
						"term": map[string]any{
							"open_scope": constmap.Enable,
						},
					},
				},
			},
		},
		"aggs": map[string]any{
			"hot_holidays": map[string]any{
				"terms": map[string]any{
					"field": "day_tags",
					"size":  5,
					"order": map[string]any{
						"_count": "desc",
					},
				},
			},
		},
	}

	// 执行ES聚合查询
	var result struct {
		Aggregations struct {
			HotHolidays struct {
				Buckets []struct {
					Key      string `json:"key"`
					DocCount int64  `json:"doc_count"`
				} `json:"buckets"`
			} `json:"hot_holidays"`
		} `json:"aggregations"`
	}

	if err := es.SearchWithAggregations(constmap.EsIndexWish, query, &result); err != nil {
		return make([]beans.WishHotDayTag, 0), err
	}

	// 构建结果
	var hotHolidays = make([]beans.WishHotDayTag, 0)
	for _, bucket := range result.Aggregations.HotHolidays.Buckets {
		hotHolidays = append(hotHolidays, beans.WishHotDayTag{
			Name:  bucket.Key,
			Count: bucket.DocCount,
		})
	}

	// 缓存结果
	v.Data = &hotHolidays
	if err := my_cache.Set(ckey, v, constmap.TimeDur1d+utils.RandTime(time.Hour)); err != nil {
		// 缓存失败不影响返回结果，只记录错误
		fmt.Printf("缓存热门节假日数据失败: %v\n", err)
	}

	return hotHolidays, nil
}

// LoadWishes 批量查询wish数据，类似user_biz.LoadUsers
func LoadWishes(db *gorm.DB, wishIds []uint) map[uint]*models.Wish {
	if len(wishIds) == 0 {
		return map[uint]*models.Wish{}
	}

	var wishes []*models.Wish
	db.Find(&wishes, wishIds)

	return slice.KeyBy(wishes, func(wish *models.Wish) uint {
		return wish.ID
	})
}

// LoadWishTags 批量查询标签数据
func LoadWishTags(db *gorm.DB, tagIds []uint) map[uint]*models.WishTag {
	if len(tagIds) == 0 {
		return map[uint]*models.WishTag{}
	}

	var tags []*models.WishTag
	db.Find(&tags, tagIds)

	return slice.KeyBy(tags, func(tag *models.WishTag) uint {
		return tag.ID
	})
}

// 查询心愿单所有成员数据
func LoadWishMembers(db *gorm.DB, wishId uint) map[uint]*models.WishMember {
	var members []*models.WishMember
	db.Where("wish_id = ?", wishId).Find(&members)

	return slice.KeyBy(members, func(member *models.WishMember) uint {
		return member.UserId
	})
}

func ParseDepartReturn(departDate, returnDate string) []time.Time {
	var start time.Time
	var end time.Time
	if ok, _ := regexp.MatchString("^\\d{4}-\\d{2}$", departDate); ok {
		if tm, err := time.Parse(constmap.DateFmtMonthFull, departDate); err == nil {
			start = tm
		}
	} else if !strutil.IsBlank(departDate) && !strutil.IsBlank(returnDate) {
		start, _ = time.Parse(constmap.DateFmtLong, departDate)
		end, _ = time.Parse(constmap.DateFmtLong, returnDate)
	}
	ret := make([]time.Time, 0)
	if !start.IsZero() && !end.IsZero() {
		ret = append(ret, start, end)
	} else if !start.IsZero() {
		ret = append(ret, start)
	}
	return ret
}
func GetDepartReturnStr(departDate, returnDate string) string {
	var dateStr string
	times := ParseDepartReturn(departDate, returnDate)
	switch len(times) {
	default:
		dateStr = "待定"
	case 1:
		dateStr = departDate
	case 2:
		dateStr = fmt.Sprintf("%s(%d天)", times[0].Format(constmap.DateFmtLongDashed), 1+int(times[1].Sub(times[0]).Hours()/24))
	}
	return dateStr
}

// 根据心愿单信息生成AI对话提示语
func BuildWishPrompt(wish *models.Wish, todos []models.WishTodo) string {
	var prompt strings.Builder

	// 心愿标题
	if !strutil.IsBlank(wish.Title) {
		prompt.WriteString(fmt.Sprintf("心愿标题：%s\n", wish.Title))
	}

	// 出行路线
	if !strutil.IsBlank(wish.From) && !strutil.IsBlank(wish.To) {
		prompt.WriteString(fmt.Sprintf("出行路线：从%s到%s\n", wish.From, wish.To))
	} else if !strutil.IsBlank(wish.To) {
		prompt.WriteString(fmt.Sprintf("目的地：%s\n", wish.To))
	}

	// 出行时间
	if !strutil.IsBlank(wish.DepartDate) && !strutil.IsBlank(wish.ReturnDate) {
		prompt.WriteString(fmt.Sprintf("出行时间：%s~%s\n", wish.DepartDate, wish.ReturnDate))
	} else if !strutil.IsBlank(wish.DepartDate) {
		prompt.WriteString(fmt.Sprintf("出行时间：%s\n", wish.DepartDate))
	} else {
		prompt.WriteString("出行时间：灵活安排\n")
	}

	// 出行人数
	if wish.TotalPeople > 0 {
		prompt.WriteString(fmt.Sprintf("出行人数：%d人\n", wish.TotalPeople))
	}

	// 预算安排
	if !strutil.IsBlank(wish.Budget) {
		budgetTypeText := "整体预算"
		if wish.BudgetType == constmap.WishBudgetSingle {
			budgetTypeText = "人均预算"
		}
		prompt.WriteString(fmt.Sprintf("预算安排：%s %s\n", budgetTypeText, wish.Budget))
	}

	// 心愿描述
	if !strutil.IsBlank(wish.WishDesc) {
		prompt.WriteString(fmt.Sprintf("心愿描述：%s\n", wish.WishDesc))
	}

	// 必做事项
	if len(todos) > 0 {
		prompt.WriteString("行程计划：\n")
		for i, todo := range todos {
			mustText := ""
			if todo.IsMust == constmap.Enable {
				mustText = "【必做】"
			}
			prompt.WriteString(fmt.Sprintf("  %d. %s%s\n", i+1, mustText, todo.Todo))
		}
	}

	// 成员要求
	if !strutil.IsBlank(wish.MemberDesc) {
		prompt.WriteString(fmt.Sprintf("成员要求：%s\n", wish.MemberDesc))
	}

	return prompt.String()
}

// 心愿单评论审核处理
func ReviewWishComment(db *gorm.DB, commentId uint) error {
	// 查询评论信息
	var comment models.WishComment
	if err := db.Take(&comment, commentId).Error; err != nil {
		my_logger.Errorf("查询心愿单评论失败", zap.Uint("commentId", commentId), zap.Error(err))
		return err
	}

	// 检查评论状态，只审核待审核状态的评论
	if comment.State != constmap.WishCommentStateWaitReview {
		my_logger.Infof("心愿单评论状态不需要审核", zap.Uint("commentId", commentId), zap.Int("state", int(comment.State)))
		return nil
	}

	// 使用百度文本审核
	riskCli := my_baidu.NewRiskClient()
	rsp, err := riskCli.TextCensor(comment.Content)
	if err != nil {
		my_logger.Errorf("百度文本审核失败", zap.Uint("commentId", commentId), zap.String("content", comment.Content), zap.Error(err))
		return err
	}

	my_logger.Infof("百度文本审核结果",
		zap.Uint("commentId", commentId),
		zap.String("conclusion", rsp.Conclusion),
		zap.Int("conclusionType", int(rsp.ConclusionType)))

	// 根据审核结果更新评论状态
	var newState constmap.WishCommentState
	if rsp.ConclusionType == my_baidu.ConclusionOk {
		// 审核通过
		newState = constmap.WishCommentStateApproved
		my_logger.Infof("心愿单评论审核通过", zap.Uint("commentId", commentId))
	} else {
		// 审核不通过
		newState = constmap.WishCommentStateRejected
		my_logger.Errorf("心愿单评论审核不通过",
			zap.Uint("commentId", commentId),
			zap.String("conclusion", rsp.Conclusion),
			zap.String("content", comment.Content))
	}

	// 更新评论状态
	if err := db.Model(&comment).
		Updates(models.WishComment{State: newState}).Error; err != nil {
		my_logger.Errorf("更新心愿单评论状态失败", zap.Uint("commentId", commentId), zap.Int("newState", int(newState)), zap.Error(err))
		return err
	}

	// 如果审核通过，发送消息通知
	if newState == constmap.WishCommentStateApproved {
		if err := sendCommentApprovedMessage(db, commentId, &comment); err != nil {
			// 消息发送失败不影响审核结果，只记录错误
			my_logger.Errorf("发送评论审核通过消息失败", zap.Uint("commentId", commentId), zap.Error(err))
		} else {
			my_logger.Infof("发送评论审核通过消息成功", zap.Uint("commentId", commentId))
		}
	}

	my_logger.Infof("心愿单评论审核完成", zap.Uint("commentId", commentId), zap.Int("finalState", int(newState)))
	return nil
}

// 发送心愿单关闭消息给所有成员
func SendWishCloseMessage(db *gorm.DB, wishId uint, reason string) error {
	// 查询心愿单信息，只预加载已通过审核的成员
	var wish models.Wish
	if err := db.Preload("Members", "state = ?", constmap.WishMemberStateApproved).Take(&wish, wishId).Error; err != nil {
		my_logger.Errorf("查询心愿单信息失败", zap.Uint("wishId", wishId), zap.Error(err))
		return err
	}

	// 收集需要发送消息的用户ID（发起人 + 已通过审核的成员）
	var userIds []uint

	// 添加已通过审核的成员（排除发起人，避免重复）
	for _, member := range wish.Members {
		userIds = append(userIds, member.UserId)
	}

	if len(userIds) == 0 {
		my_logger.Infof("心愿单没有需要通知的成员", zap.Uint("wishId", wishId))
		return nil
	}

	// 构建消息内容
	title := "心愿单已关闭"
	content := fmt.Sprintf("您参与的心愿单「%s」已被关闭。关闭原因：%s", wish.Title, reason)

	// 使用事务批量创建消息
	err := db.Transaction(func(tx *gorm.DB) error {
		// 创建消息模板
		messageTpl := &models.MessageTpl{
			Type:         constmap.MessageTypeSystem,
			SubType:      constmap.MessageSubTypeText,
			Title:        title,
			Content:      content,
			WishMemberId: 0, // 系统消息不关联具体成员
		}

		if err := tx.Create(messageTpl).Error; err != nil {
			return err
		}

		// 为每个用户创建消息记录
		for _, userId := range userIds {
			message := &models.Message{
				MessageTplId: messageTpl.ID,
				UserId:       userId,
				IsRead:       constmap.Disable, // 未读
			}

			if err := tx.Create(message).Error; err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		my_logger.Errorf("发送心愿单关闭消息失败", zap.Uint("wishId", wishId), zap.Error(err))
		return err
	}

	// 清除相关用户的消息缓存
	for _, userId := range userIds {
		message_biz.ClearUserMessageCacheByTypes(userId, []constmap.MessageType{constmap.MessageTypeSystem})
	}

	my_logger.Infof("成功发送心愿单关闭消息",
		zap.Uint("wishId", wishId),
		zap.String("reason", reason),
		zap.Int("userCount", len(userIds)),
		zap.Uints("userIds", userIds))

	return nil
}

// 发送评论审核通过消息（私有函数）
func sendCommentApprovedMessage(db *gorm.DB, commentId uint, comment *models.WishComment) error {
	// 查询关联的心愿单信息
	var wish models.Wish
	if err := db.Take(&wish, comment.WishId).Error; err != nil {
		return err
	}

	// 查询评论者信息
	commentUserMap := user_biz.LoadUsers(db, []uint{comment.UserId})
	commentUser, exists := commentUserMap[comment.UserId]
	if !exists {
		return fmt.Errorf("评论者信息不存在")
	}

	// 确定消息接收人
	var toUserId uint
	var title, content string

	if comment.ReplyUserId > 0 {
		// 有被回复人，给被回复人发消息
		toUserId = comment.ReplyUserId
		title = "您的评论收到新回复"
		content = fmt.Sprintf("%s 回复了您在心愿单「%s」中的评论：%s", commentUser.Nickname, wish.Title, comment.Content)
	} else {
		// 没有被回复人，给心愿单发起人发消息
		toUserId = wish.UserId
		title = "心愿单收到新评论"
		content = fmt.Sprintf("%s 评论了您的心愿单「%s」：%s", commentUser.Nickname, wish.Title, comment.Content)
	}

	// 如果接收人是评论者本人，不发送消息
	if toUserId == comment.UserId {
		return nil
	}

	// 使用事务创建消息模板和用户消息记录
	err := db.Transaction(func(tx *gorm.DB) error {
		// 创建消息模板
		messageTpl := &models.MessageTpl{
			Type:          constmap.MessageTypeComment,
			SubType:       constmap.MessageSubTypeText,
			Title:         title,
			Content:       content,
			WishCommentId: commentId,
			WishMemberId:  0,
		}

		if err := tx.Create(messageTpl).Error; err != nil {
			return err
		}

		// 创建用户消息记录（未读状态）
		message := &models.Message{
			MessageTplId: messageTpl.ID,
			UserId:       toUserId,
			IsRead:       constmap.Disable, // 未读
		}

		if err := tx.Create(message).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	// 清除相关缓存
	message_biz.ClearUserMessageCacheByTypes(toUserId, []constmap.MessageType{constmap.MessageTypeComment})

	return nil
}
